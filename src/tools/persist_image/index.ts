import type { ToolCallback } from '@modelcontextprotocol/sdk/server/mcp.js';
import type { Logger } from 'winston';
import { z } from 'zod';

export const schema = {
  link: z.string().url().describe('URL to the full-quality image'),
  path: z.string().describe('Full path where to save the image'),
} as const;

export function getHandler(logger: Logger) {
  const handler: ToolCallback<typeof schema> = async ({ link, path }) => {
    logger.info('persist_image() tool called', { link, path });

    return {
      content: [
        {
          type: 'text' as const,
          text: 'foo',
        },
      ],
    };
  };

  return handler;
}
