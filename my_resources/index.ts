import { McpServer, ResourceTemplate } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { resolve } from 'node:path';
import winston, { transports } from 'winston';

import { version } from '../package.json';
import { schema as searchImageSchema, getHandler as getSearchImageHandler } from '~/tools/search_image';

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.prettyPrint(),
  defaultMeta: { service: 'mcp-google-images-search' },
  transports: [new transports.File({ filename: resolve(__dirname, '..', '.logs', 'info.log') })],
});
const server = new McpServer({ name: 'Google images search', version });

server.tool('search_image', 'Search for images with Google images service', searchImageSchema, getSearchImageHandler(logger));

const userResources: Array<{ uri: string; name: string; description: string }> = [
  { uri: 'users://alice', name: '<PERSON>', description: 'Just a normal person' },
  { uri: 'users://srigi', name: 'Srigi Dev', description: 'Advanced full-stack web-developer' },
];
const imageResources: Array<{ uri: string; name: string; description?: string; mimeType: string; remoteUrl: string; blob: string }> = [
  {
    uri: 'images://001',
    name: 'Image 001',
    //description: 'Edge browser logo',
    mimeType: 'image/png',
    remoteUrl: 'https://i.postimg.cc/CMXJP00H/edge.png',
    blob: 'data:image/png;base64,iVBORw0KGgoAAAANS...',
  },
  {
    uri: 'images://002',
    name: 'Image 002',
    //description: 'Panda eating bamboo stick',
    mimeType: 'image/jpeg',
    remoteUrl: 'https://i.postimg.cc/X7C91yNQ/i-Stock-144804461.jpg',
    blob: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQ...',
  },
];

server.resource('users/all', new ResourceTemplate('users://', { list: undefined }), async (uri) => {
  logger.info("ReadResourceCallback('users/all') called", { uri });

  if (uri.href !== 'users://') {
    return { isError: true, contents: [{ uri: uri.href, text: 'Requested resource not found!' }] };
  }

  return {
    contents: [
      {
        uri: uri.href,
        text: `List of all users:${userResources.reduce((acc, r) => `${acc}\n - ${r.uri}`, '')}`,
      },
    ],
  };
});
server.resource(
  'users/<username>',
  new ResourceTemplate('users://{username}', {
    list: () => {
      logger.info("ListResourcesCallback('users://{username}') called");

      return { resources: userResources };
    },
  }),
  async (uri) => {
    logger.info("ReadResourceCallback('users/<username>') called", { uri });

    const matches = uri.href.match(/^users:\/\/(.+)?$/);
    if (matches) {
      const username = decodeURIComponent(matches[1]);
      const userResource = userResources.find((r) => r.uri.includes(username));
      if (userResource == null) {
        return { isError: true, contents: [{ uri: uri.href, text: 'Requested resource not found!' }] };
      }

      return {
        contents: [
          {
            uri: uri.href,
            text: `User properties:\n  name: ${userResource.name}\n  description: ${userResource?.description}`,
          },
        ],
      };
    }

    throw new Error('Resource not found');
  },
);
server.tool('add_user', { name: z.string(), description: z.string() }, async ({ name, description }) => {
  const newUserResource = { uri: `users://${name.toLowerCase()}`, name, description };
  userResources.push(newUserResource);

  return {
    content: [
      {
        type: 'text',
        text: `New User resource: ${JSON.stringify(newUserResource, null, 2)}`,
      },
    ],
  };
});

server.resource('images/<imageId>', new ResourceTemplate('images://{imageId}', { list: undefined }), async (uri) => {
  logger.info("ReadResourceCallback('images/<imageId>') called", { uri });

  const matches = uri.href.match(/^images:\/\/(.+)?$/);
  if (matches) {
    const imageId = decodeURIComponent(matches[1]);
    const imageResource = imageResources.find((r) => r.uri.includes(imageId));
    logger.info('ReadResourceCallback::imageResource', { imageResource });
    if (imageResource == null) {
      return { isError: true, contents: [{ uri: uri.href, text: 'Requested resource not found!' }] };
    }

    logger.info('ReadResourceCallback::will-return >>>');
    return {
      contents: [
        {
          uri: uri.href,
          text: imageResource.name,
          blob: imageResource.blob,
          mimeType: imageResource.mimeType,
        },
      ],
    };
  }

  throw new Error('Resource not found');
});

server.connect(new StdioServerTransport()).then(() => logger.info('server connected'));
