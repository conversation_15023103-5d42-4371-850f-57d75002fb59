# Knowledge base May 30th, 2025

## Claude

### MCP tools

- knows how to call MCP tool

### Resources

- will only access the ListResourcesCallback initially
- doesn't know how to call ReadResourceTemplateCallback during chat session
- will access the ListResourcesCallback with the next chat message
- can render nice list of Resources in the prompt area obtained from ListResourcesCallback

---

## Cline

### MCP tools

- knows how to call MCP tool
- MCP response could render images in chat history! - [`<ChatRow.tsx>`](https://github.com/cline/cline/blob/v3.17.8/webview-ui/src/components/chat/ChatRow.tsx#L971), [`<McpResponseDisplay>`](https://github.com/cline/cline/blob/v3.17.8/webview-ui/src/components/mcp/chat-display/McpResponseDisplay.tsx#L327)

### Resources

- does know how to call ReadResourceTemplateCallback during chat session
- only takes `content[].text` from the above callback response, no other special handling (plaintext)

---

## Roo

### MCP tools

- knows how to call MCP tool
- any MCP response is [rendered into `<CodeAccordian>`](https://github.com/RooCodeInc/Roo-Code/blob/v3.19.0/webview-ui/src/components/chat/ChatRow.tsx#L937), images cannot be rendered in chat history

### Resources

- does know how to call ReadResourceTemplateCallback during chat session
- takes `content[].text` and `content[].mimeType` from the above callback response
- if `mimeType` starts with `image` it is pushed into `images` array
- [images processing starting point](https://github.com/RooCodeInc/Roo-Code/blob/v3.19.0/src/core/tools/accessMcpResourceTool.ts#L80)
- images can be currently processed only by frontier models :( (no local LLM)
