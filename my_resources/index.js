"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mcp_js_1 = require("@modelcontextprotocol/sdk/server/mcp.js");
const stdio_js_1 = require("@modelcontextprotocol/sdk/server/stdio.js");
const node_path_1 = require("node:path");
const winston_1 = __importStar(require("winston"));
const package_json_1 = require("../package.json");
const search_image_1 = require("../src/tools/search_image");
const logger = winston_1.default.createLogger({
    level: 'info',
    format: winston_1.default.format.prettyPrint(),
    defaultMeta: { service: 'mcp-google-images-search' },
    transports: [new winston_1.transports.File({ filename: (0, node_path_1.resolve)(__dirname, '..', '.logs', 'info.log') })],
});
const server = new mcp_js_1.McpServer({ name: 'Google images search', version: package_json_1.version });
server.tool('search_image', 'Search for images with Google images service', search_image_1.schema, (0, search_image_1.getHandler)(logger));
const userResources = [
    { uri: 'users://alice', name: 'Alice Doe', description: 'Just a normal person' },
    { uri: 'users://srigi', name: 'Srigi Dev', description: 'Advanced full-stack web-developer' },
];
const imageResources = [
    {
        uri: 'images://001',
        name: 'Image 001',
        //description: 'Edge browser logo',
        mimeType: 'image/png',
        remoteUrl: 'https://i.postimg.cc/CMXJP00H/edge.png',
        blob: 'data:image/png;base64,iVBORw0KGgoAAAANS...',
    },
    {
        uri: 'images://002',
        name: 'Image 002',
        //description: 'Panda eating bamboo stick',
        mimeType: 'image/jpeg',
        remoteUrl: 'https://i.postimg.cc/X7C91yNQ/i-Stock-144804461.jpg',
        blob: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQ...',
    },
];
server.resource('users/all', new mcp_js_1.ResourceTemplate('users://', { list: undefined }), async (uri) => {
    logger.info("ReadResourceCallback('users/all') called", { uri });
    if (uri.href !== 'users://') {
        return { isError: true, contents: [{ uri: uri.href, text: 'Requested resource not found!' }] };
    }
    return {
        contents: [
            {
                uri: uri.href,
                text: `List of all users:${userResources.reduce((acc, r) => `${acc}\n - ${r.uri}`, '')}`,
            },
        ],
    };
});
server.resource('users/<username>', new mcp_js_1.ResourceTemplate('users://{username}', {
    list: () => {
        logger.info("ListResourcesCallback('users://{username}') called");
        return { resources: userResources };
    },
}), async (uri) => {
    logger.info("ReadResourceCallback('users/<username>') called", { uri });
    const matches = uri.href.match(/^users:\/\/(.+)?$/);
    if (matches) {
        const username = decodeURIComponent(matches[1]);
        const userResource = userResources.find((r) => r.uri.includes(username));
        if (userResource == null) {
            return { isError: true, contents: [{ uri: uri.href, text: 'Requested resource not found!' }] };
        }
        return {
            contents: [
                {
                    uri: uri.href,
                    text: `User properties:\n  name: ${userResource.name}\n  description: ${userResource?.description}`,
                },
            ],
        };
    }
    throw new Error('Resource not found');
});
server.tool('add_user', { name: z.string(), description: z.string() }, async ({ name, description }) => {
    const newUserResource = { uri: `users://${name.toLowerCase()}`, name, description };
    userResources.push(newUserResource);
    return {
        content: [
            {
                type: 'text',
                text: `New User resource: ${JSON.stringify(newUserResource, null, 2)}`,
            },
        ],
    };
});
server.resource('images/<imageId>', new mcp_js_1.ResourceTemplate('images://{imageId}', { list: undefined }), async (uri) => {
    logger.info("ReadResourceCallback('images/<imageId>') called", { uri });
    const matches = uri.href.match(/^images:\/\/(.+)?$/);
    if (matches) {
        const imageId = decodeURIComponent(matches[1]);
        const imageResource = imageResources.find((r) => r.uri.includes(imageId));
        logger.info('ReadResourceCallback::imageResource', { imageResource });
        if (imageResource == null) {
            return { isError: true, contents: [{ uri: uri.href, text: 'Requested resource not found!' }] };
        }
        logger.info('ReadResourceCallback::will-return >>>');
        return {
            contents: [
                {
                    uri: uri.href,
                    text: imageResource.name,
                    blob: imageResource.blob,
                    mimeType: imageResource.mimeType,
                },
            ],
        };
    }
    throw new Error('Resource not found');
});
server.connect(new stdio_js_1.StdioServerTransport()).then(() => logger.info('server connected'));
